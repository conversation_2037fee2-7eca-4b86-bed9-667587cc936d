'use server'
import { auth } from "@/auth"
import { Topic } from "@/generated/prisma"
import { prisma } from "@/lib"
import { revalidatePath } from "next/cache"
import { redirect } from "next/navigation"
import { z } from "zod"

const createTopicSchema = z.object({
    name: z.string().min(3).regex(/^[a-z-]+$/, { message: "Must be Lower case letter" }),
    description: z.string().min(3)
})

type CreateTopicFormState = {
    errors: {
        name?: string[],
        description?: string[],
        formError?: string[]
    }
}

export const createTopic = async (prevState: CreateTopicFormState, formData: FormData): Promise<CreateTopicFormState> => {

    const res = createTopicSchema.safeParse({
        name: formData.get('name'),
        description: formData.get('description')
    })
    // { success: true, data: { name: 'sffsfdfs', description: 'sefretrer' } }
    console.log(res);
    if (!res.success) {
        return {
            errors: res.error.flatten().fieldErrors
        }
    }

    const session = await auth();
    if (!session || !session.user) {
        return {
            errors: {
                formError: ["You have to login first"]
            }
        }
    }
    let topic: Topic;
    try {
        topic = await prisma.topic.create({
            data: {
                slug: res.data.name,
                description: res.data.description
            }
        })
    } catch (error) {
        if (error instanceof Error) {
            return {
                errors: {
                    formError: [error.message]
                }
            }
        } else {
            return {
                errors: {
                    formError: ["Something went wrong."]
                }
            }
        }
    }
    revalidatePath("/");
    redirect(`/topics/${topic.slug}`);
}
