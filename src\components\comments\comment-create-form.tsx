'use client'
import React, { useActionState, useState } from 'react'
import { Button } from '../ui/button'
import { Textarea } from '../ui/textarea'
import { createComment } from '@/actions/create-comment'
import { Loader2, MessageSquare, AlertCircle } from 'lucide-react'
import { Card, CardContent } from '../ui/card'

type CommentCreateFormProps = {
    postId: string
    parentID?: string;
    startOpen?: boolean;
}

const CommentCreateForm = ({ postId, parentID, startOpen }: CommentCreateFormProps) => {
    const [open, setOpen] = useState(startOpen)
    const [formState, action, isPending] = useActionState(createComment.bind(null, { postId, parentID }), { errors: {} });

    return (
        <div className="space-y-4">
            {!startOpen && (
                <Button
                    size="sm"
                    variant="ghost"
                    onClick={() => setOpen(!open)}
                    className="text-muted-foreground hover:text-foreground"
                >
                    <MessageSquare className="h-4 w-4 mr-2" />
                    {open ? 'Cancel' : 'Reply'}
                </Button>
            )}

            {open && (
                <Card>
                    <CardContent className="pt-6">
                        <form action={action} className="space-y-4">
                            <Textarea
                                name="content"
                                placeholder={parentID ? "Write a reply..." : "Share your thoughts..."}
                                className="min-h-[100px] resize-none"
                            />

                            {formState.errors.content && (
                                <div className="flex items-center space-x-2 text-sm text-destructive">
                                    <AlertCircle className="h-4 w-4" />
                                    <span>{formState.errors.content}</span>
                                </div>
                            )}

                            {formState.errors.formError && (
                                <div className="p-3 rounded-md bg-destructive/10 border border-destructive/20">
                                    <div className="flex items-center space-x-2 text-sm text-destructive">
                                        <AlertCircle className="h-4 w-4" />
                                        <span>{formState.errors.formError}</span>
                                    </div>
                                </div>
                            )}

                            <div className="flex justify-end space-x-2">
                                {!startOpen && (
                                    <Button
                                        type="button"
                                        variant="outline"
                                        size="sm"
                                        onClick={() => setOpen(false)}
                                    >
                                        Cancel
                                    </Button>
                                )}
                                <Button
                                    disabled={isPending}
                                    size="sm"
                                    type="submit"
                                >
                                    {isPending ? (
                                        <>
                                            <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                                            Posting...
                                        </>
                                    ) : (
                                        parentID ? "Reply" : "Post Comment"
                                    )}
                                </Button>
                            </div>
                        </form>
                    </CardContent>
                </Card>
            )}
        </div>
    )
}

export default CommentCreateForm