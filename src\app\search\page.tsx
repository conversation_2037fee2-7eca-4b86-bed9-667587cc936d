import PostList from '@/components/posts/post-list'
import { fetchPostBysearch } from '@/lib/query/post'
import React from 'react'

type SearchPageProps = {
    searchParams: Promise<{ term: string }>
}

const SearchPage = async ({ searchParams }: SearchPageProps) => {
    const { term } = await searchParams

    return (
        <div>
            <h1 className="text-blue-600 font-medium italic">
                Search result for {term}
            </h1>
            <PostList fetchData={() => fetchPostBysearch(term)} />
        </div>
    )
}

export default SearchPage