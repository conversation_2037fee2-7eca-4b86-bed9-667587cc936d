import PostList from '@/components/posts/post-list'
import { fetchPostBysearch } from '@/lib/query/post'
import React from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Search } from 'lucide-react'

type SearchPageProps = {
    searchParams: Promise<{ term: string }>
}

const SearchPage = async ({ searchParams }: SearchPageProps) => {
    const { term } = await searchParams

    if (!term) {
        return (
            <div className="text-center py-12">
                <Search className="mx-auto h-12 w-12 text-muted-foreground" />
                <h3 className="mt-4 text-lg font-semibold">Search Discussions</h3>
                <p className="text-muted-foreground">Enter a search term to find posts and discussions</p>
            </div>
        )
    }

    return (
        <div className="space-y-6">
            {/* Search Header */}
            <Card>
                <CardHeader>
                    <div className="flex items-center space-x-3">
                        <Search className="h-6 w-6 text-primary" />
                        <div>
                            <CardTitle className="text-2xl">Search Results</CardTitle>
                            <div className="flex items-center space-x-2 mt-2">
                                <span className="text-muted-foreground">Results for:</span>
                                <Badge variant="secondary" className="text-base px-3 py-1">
                                    {term}
                                </Badge>
                            </div>
                        </div>
                    </div>
                </CardHeader>
            </Card>

            {/* Results */}
            <PostList fetchData={() => fetchPostBysearch(term)} />
        </div>
    )
}

export default SearchPage