import type { Post, VoteType } from "@/generated/prisma";
import { prisma } from "@/lib";

export type PostWithData = Post & {
    topic: { slug: string }
    _count: { comments: number, votes: number }
    user: { name: string | null, image: string | null }
    votes: Array<{ type: VoteType, userId: string }>
}

export const fetchPostByTopicSlug = async (slug: string)
    : Promise<PostWithData[]> => {

    return prisma.post.findMany({
        where: {
            topic: { slug }
        },
        include: {
            topic: { select: { slug: true } },
            _count: { select: { comments: true, votes: true } },
            user: { select: { name: true, image: true } },
            votes: { select: { type: true, userId: true } }
        }
    })
}

export const fetchTopPost = async ()
    : Promise<PostWithData[]> => {

    return prisma.post.findMany({
        orderBy: [
            {
                comments: { _count: 'desc' }
            }
        ],
        include: {
            topic: { select: { slug: true } },
            _count: { select: { comments: true, votes: true } },
            user: { select: { name: true, image: true } },
            votes: { select: { type: true, userId: true } }
        },
        take: 5
    })
}

export const fetchPostBysearch = async (term: string): Promise<PostWithData[]> => {
    return prisma.post.findMany({
        include: {
            topic: { select: { slug: true } },
            _count: { select: { comments: true, votes: true } },
            user: { select: { name: true, image: true } },
            votes: { select: { type: true, userId: true } }
        },
        where: {
            OR: [
                { title: { contains: term } },
                { content: { contains: term } }
            ]
        }
    })
}
