
import React from 'react'
import AuthHeader from '@/components/auth-header'
import SearchInput from './search-input'
import Link from 'next/link'
import { MessageSquare, Hash } from 'lucide-react'
import { ThemeToggle } from './theme-toggle'
import { Button } from './ui/button'

const Header = async () => {
    return (
        <header className='sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60'>
            <div className='container mx-auto max-w-6xl px-4'>
                <div className='flex h-16 items-center justify-between'>
                    {/* Logo & Navigation */}
                    <div className="flex items-center space-x-6">
                        <Link href="/" className='flex items-center space-x-2'>
                            <MessageSquare className='h-6 w-6 text-primary' />
                            <h1 className='font-bold text-xl bg-gradient-to-r from-primary to-primary/60 bg-clip-text text-transparent'>
                                Discuss
                            </h1>
                        </Link>

                        <nav className="hidden md:flex items-center space-x-4">
                            <Link href="/topics">
                                <Button variant="ghost" size="sm">
                                    <Hash className="h-4 w-4 mr-2" />
                                    Topics
                                </Button>
                            </Link>
                        </nav>
                    </div>

                    {/* Search - Hidden on mobile, shown on tablet+ */}
                    <div className='hidden md:flex flex-1 max-w-md mx-8'>
                        <SearchInput />
                    </div>

                    {/* Auth */}
                    <div className='flex items-center gap-2'>
                        <ThemeToggle />
                        <AuthHeader />
                    </div>
                </div>

                {/* Mobile Search */}
                <div className='md:hidden pb-4'>
                    <SearchInput />
                </div>
            </div>
        </header>
    )
}

export default Header