import { But<PERSON> } from "@/components/ui/button"
import { signIn} from "../actions/sign-in";
import { signOut } from "../actions/sign-out";
import { auth } from "@/auth";
import TopicCreateForm from "@/components/topics/TopicCreateForm";
import PostList from "@/components/posts/post-list";
import { fetchTopPost } from "@/lib/query/post";

export default async function Home() {

  // const session = await auth();

  return (
    <div className="space-y-6">
      {/* Hero Section */}
      <div className="text-center space-y-4 py-8">
        <h1 className="text-4xl font-bold bg-gradient-to-r from-primary to-primary/60 bg-clip-text text-transparent">
          Welcome to Discuss
        </h1>
        <p className="text-muted-foreground text-lg max-w-2xl mx-auto">
          Join conversations, share ideas, and connect with a community of passionate discussers.
        </p>
      </div>

      {/* Main Content */}
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        <div className="lg:col-span-3 space-y-6">
          <div className="flex items-center justify-between">
            <h2 className="text-2xl font-semibold">Latest Discussions</h2>
            <div className="lg:hidden">
              <TopicCreateForm />
            </div>
          </div>
          <PostList fetchData={() => fetchTopPost()} />
        </div>

        {/* Sidebar - Hidden on mobile */}
        <div className="hidden lg:block space-y-6">
          <div className="sticky top-24">
            <TopicCreateForm />
          </div>
        </div>
      </div>
    </div>
  );
}
