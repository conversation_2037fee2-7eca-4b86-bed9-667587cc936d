import React from 'react'
import { Avatar, AvatarFallback, AvatarImage } from '../ui/avatar'
import { fetchCommentByPost } from '@/lib/query/comment'
import CommentCreateForm from './comment-create-form'

type CommentShowProps = {
    postId: string,
    commentId: string
}

const CommentShow = async ({ postId, commentId }: CommentShowProps) => {
    const comments = await fetchCommentByPost(postId)
    const comment = comments.find((comment) => comment.id === commentId);
    if (!comment) return null;
    const childern = comments.filter((comment) => comment.parentId === commentId);
    return (
        <div className='className="m-4 p-4 border"'>
            <div className='flex gap-3'>
                <Avatar>
                    <AvatarImage src={comment.user.image || ""} />
                    <AvatarFallback>CN</AvatarFallback>
                </Avatar>
                <div className="flex-1 space-y-3">
                    <p className="text-gray-500 text-sm font-medium">
                        {comment.user.name}
                    </p>
                    <p className="text-gray-800">{comment.content}</p>
                    <CommentCreateForm postId={comment.postId} parentID={comment.id} />
                </div>
            </div>
            {childern.map((comment) => (
                <CommentShow key={comment.id} postId={comment.postId} commentId={comment.id} />
            ))}
        </div>
    )
}

export default CommentShow