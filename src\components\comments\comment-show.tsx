import React from 'react'
import { Avatar, AvatarFallback, AvatarImage } from '../ui/avatar'
import { fetchCommentByPost } from '@/lib/query/comment'
import CommentCreateForm from './comment-create-form'
import { Card, CardContent } from '../ui/card'
import { Clock } from 'lucide-react'

type CommentShowProps = {
    postId: string,
    commentId: string
}

const CommentShow = async ({ postId, commentId }: CommentShowProps) => {
    const comments = await fetchCommentByPost(postId)
    const comment = comments.find((comment) => comment.id === commentId);
    if (!comment) return null;
    const children = comments.filter((comment) => comment.parentId === commentId);

    return (
        <div className="space-y-4">
            <Card>
                <CardContent className="pt-6">
                    <div className="flex space-x-3">
                        <Avatar className="h-8 w-8">
                            <AvatarImage src={comment.user.image || ""} />
                            <AvatarFallback className="text-xs">
                                {comment.user.name?.charAt(0) || 'U'}
                            </AvatarFallback>
                        </Avatar>

                        <div className="flex-1 space-y-3">
                            <div className="flex items-center space-x-2 text-sm">
                                <span className="font-medium">{comment.user.name}</span>
                                <span className="text-muted-foreground">•</span>
                                <div className="flex items-center space-x-1 text-muted-foreground">
                                    <Clock className="h-3 w-3" />
                                    <span>{new Date(comment.createdAt).toLocaleDateString()}</span>
                                </div>
                            </div>

                            <p className="text-foreground leading-relaxed">{comment.content}</p>

                            <CommentCreateForm postId={comment.postId} parentID={comment.id} />
                        </div>
                    </div>
                </CardContent>
            </Card>

            {children.length > 0 && (
                <div className="ml-8 space-y-4 border-l-2 border-muted pl-4">
                    {children.map((childComment) => (
                        <CommentShow
                            key={childComment.id}
                            postId={childComment.postId}
                            commentId={childComment.id}
                        />
                    ))}
                </div>
            )}
        </div>
    )
}

export default CommentShow