'use client'
import React from 'react'
import { Input } from './ui/input'
import { useSearchParams } from 'next/navigation'
import { search } from '@/actions/search';
import { Search } from 'lucide-react'

const SearchInput = () => {
    const searchParams = useSearchParams();

    return (
        <form action={search} className="relative w-full max-w-sm">
            <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                    defaultValue={searchParams.get("term") || ""}
                    type="text"
                    name="term"
                    placeholder="Search discussions..."
                    className="pl-10 pr-4"
                />
            </div>
        </form>
    )
}

export default SearchInput