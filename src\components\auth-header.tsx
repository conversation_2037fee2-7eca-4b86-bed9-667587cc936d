"use client"
import React from 'react'
import { useSession } from 'next-auth/react'
import { Button } from './ui/button'
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Separator } from "@/components/ui/separator"
import { signOut } from '@/actions/sign-out'
import { signIn } from '@/actions/sign-in'
import { LogOut } from "lucide-react"
const AuthHeader = () => {
    const session = useSession();

    if(session.status === 'loading') return null;

    let authContent: React.ReactNode;

    if (session.data?.user) {
        authContent = (
            <Popover>
                <PopoverTrigger asChild>
                    <Avatar>
                        <AvatarImage src={session.data.user.image || ""} alt='Profile Img' />
                        <AvatarFallback>CN</AvatarFallback>
                    </Avatar>
                </PopoverTrigger>
                <PopoverContent>
                    <h1>{session.data.user.name}</h1>
                    <Separator className="py-2" />
                    <form action={signOut}>
                        <Button type='submit'> <LogOut /> Sign out</Button>
                    </form>
                </PopoverContent>
            </Popover>
        )

    } else {
        authContent = ( 
            <>
                <form action={signIn}>
                    <Button variant={'outline'} >Sign in</Button>
                </form>
                <form action={signOut}>
                    <Button >Sign Out</Button>
                </form>
            </>
        )
    }

    return authContent;
}

export default AuthHeader
