"use client"
import React from 'react'
import { useSession } from 'next-auth/react'
import { Button } from './ui/button'
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Separator } from "@/components/ui/separator"
import { signOut } from '@/actions/sign-out'
import { signIn } from '@/actions/sign-in'
import { LogOut, User } from "lucide-react"
import Link from 'next/link'
const AuthHeader = () => {
    const session = useSession();

    if(session.status === 'loading') return null;

    let authContent: React.ReactNode;

    if (session.data?.user) {
        authContent = (
            <Popover>
                <PopoverTrigger asChild>
                    <Avatar>
                        <AvatarImage src={session.data.user.image || ""} alt='Profile Img' />
                        <AvatarFallback>CN</AvatarFallback>
                    </Avatar>
                </PopoverTrigger>
                <PopoverContent className="w-56">
                    <div className="space-y-4">
                        <div className="space-y-2">
                            <h4 className="font-medium">{session.data.user.name}</h4>
                            <p className="text-sm text-muted-foreground">{session.data.user.email}</p>
                        </div>

                        <Separator />

                        <div className="space-y-2">
                            <Link href={`/profile/${session.data.user.id}`}>
                                <Button variant="ghost" className="w-full justify-start">
                                    <User className="h-4 w-4 mr-2" />
                                    View Profile
                                </Button>
                            </Link>

                            <form action={signOut}>
                                <Button type='submit' variant="ghost" className="w-full justify-start">
                                    <LogOut className="h-4 w-4 mr-2" />
                                    Sign out
                                </Button>
                            </form>
                        </div>
                    </div>
                </PopoverContent>
            </Popover>
        )

    } else {
        authContent = ( 
            <>
                <form action={signIn}>
                    <Button variant={'outline'} >Sign in</Button>
                </form>
                <form action={signOut}>
                    <Button >Sign Out</Button>
                </form>
            </>
        )
    }

    return authContent;
}

export default AuthHeader
