import CommentCreateForm from '@/components/comments/comment-create-form';
import CommentList from '@/components/comments/comment-list';
import PostShow from '@/components/posts/post-show';
import { Button } from '@/components/ui/button';
import { ChevronLeft } from 'lucide-react';
import Link from 'next/link';
import React from 'react'

type SinglePostProps = {
  params: Promise<{ slug: string, postId: string }>
}

const SinglePost = async ({ params }: SinglePostProps) => {
  const { slug, postId } = await params;

  return (
    <div className='space-y-3'>
      <Link href={`/topics/${slug}`}>
        <Button variant={'link'} >
          <ChevronLeft />
          Back to {slug}
        </Button>
      </Link>
      <PostShow postId={postId} />
      <CommentCreateForm postId={postId} startOpen />
      <CommentList postId={postId} />
    </div>
  )
}
// http://localhost:3000/topics/javascript/posts/cme3ymwkl0001u6kwxnke1me1

export default SinglePost