import CommentCreateForm from '@/components/comments/comment-create-form';
import CommentList from '@/components/comments/comment-list';
import PostShow from '@/components/posts/post-show';
import { Button } from '@/components/ui/button';
import { ChevronLeft } from 'lucide-react';
import Link from 'next/link';
import React from 'react'

type SinglePostProps = {
  params: Promise<{ slug: string, postId: string }>
}

const SinglePost = async ({ params }: SinglePostProps) => {
  const { slug, postId } = await params;

  return (
    <div className='space-y-6'>
      {/* Breadcrumb */}
      <div className="flex items-center space-x-2 text-sm text-muted-foreground">
        <Link href="/" className="hover:text-foreground transition-colors">
          Home
        </Link>
        <span>/</span>
        <Link href={`/topics/${slug}`} className="hover:text-foreground transition-colors">
          {slug}
        </Link>
        <span>/</span>
        <span className="text-foreground">Post</span>
      </div>

      {/* Back Button */}
      <Link href={`/topics/${slug}`}>
        <Button variant="ghost" className="mb-4">
          <ChevronLeft className="h-4 w-4 mr-2" />
          Back to {slug}
        </Button>
      </Link>

      {/* Post Content */}
      <div className="space-y-6">
        <PostShow postId={postId} />

        {/* Comments Section */}
        <div className="border-t pt-6">
          <CommentCreateForm postId={postId} startOpen />
          <div className="mt-6">
            <CommentList postId={postId} />
          </div>
        </div>
      </div>
    </div>
  )
}
// http://localhost:3000/topics/javascript/posts/cme3ymwkl0001u6kwxnke1me1

export default SinglePost