import type { <PERSON>ada<PERSON> } from "next";
import { Geist, <PERSON>eist_Mono } from "next/font/google";
import "./globals.css";
import { SessionProvider } from "next-auth/react";
import Header from "@/components/Header";
import Footer from "@/components/footer";
import { ThemeProvider } from "@/components/theme-provider";
import { Toaster } from "@/components/ui/sonner";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Discuss - Community Discussion Platform",
  description: "Join discussions, share ideas, and connect with like-minded people in our community platform",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased min-h-screen bg-background`}
      >
        <ThemeProvider
          attribute="class"
          defaultTheme="system"
          enableSystem
          disableTransitionOnChange
        >
          <div className="min-h-screen flex flex-col">
            <SessionProvider>
              <Header />
              <main className="flex-1 container mx-auto max-w-6xl px-4 py-6">
                {children}
              </main>
              <Footer />
            </SessionProvider>
          </div>
          <Toaster />
        </ThemeProvider>
      </body>
    </html>
  );
}
