import React from 'react'
import { Card, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { PostWithData } from '@/lib/query/post'
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { MessageSquare, Clock, User } from 'lucide-react'
import Link from 'next/link'

type PostListProps = {
    fetchData: () => Promise<PostWithData[]>
}

const PostList = async ({ fetchData }: PostListProps) => {
    const posts = await fetchData();

    if (posts.length === 0) {
        return (
            <div className="text-center py-12">
                <MessageSquare className="mx-auto h-12 w-12 text-muted-foreground" />
                <h3 className="mt-4 text-lg font-semibold">No posts yet</h3>
                <p className="text-muted-foreground">Be the first to start a discussion!</p>
            </div>
        )
    }

    return (
        <div className='space-y-4'>
            {posts.map((post) => (
                <Link key={post.id} href={`/topics/${post.topic.slug}/posts/${post.id}`}>
                    <Card className="hover:shadow-md transition-shadow cursor-pointer group">
                        <CardHeader className="space-y-4">
                            <CardTitle className="group-hover:text-primary transition-colors line-clamp-2">
                                {post.title}
                            </CardTitle>

                            <div className="flex items-center justify-between text-sm text-muted-foreground">
                                <div className="flex items-center space-x-4">
                                    <div className="flex items-center space-x-2">
                                        <Avatar className="h-6 w-6">
                                            <AvatarImage src={post.user.image || ""} />
                                            <AvatarFallback className="text-xs">
                                                {post.user.name?.charAt(0) || 'U'}
                                            </AvatarFallback>
                                        </Avatar>
                                        <span className="hidden sm:inline">By {post.user.name}</span>
                                        <span className="sm:hidden">
                                            <User className="h-4 w-4" />
                                        </span>
                                    </div>

                                    <div className="flex items-center space-x-1">
                                        <Clock className="h-4 w-4" />
                                        <span className="hidden sm:inline">
                                            {new Date(post.createdAt).toLocaleDateString()}
                                        </span>
                                        <span className="sm:hidden">
                                            {new Date(post.createdAt).toLocaleDateString('en-US', {
                                                month: 'short',
                                                day: 'numeric'
                                            })}
                                        </span>
                                    </div>
                                </div>

                                <div className="flex items-center space-x-1">
                                    <MessageSquare className="h-4 w-4" />
                                    <span>{post._count.comments}</span>
                                    <span className="hidden sm:inline">comments</span>
                                </div>
                            </div>

                            {post.content && (
                                <p className="text-muted-foreground line-clamp-2 text-sm">
                                    {post.content.substring(0, 150)}...
                                </p>
                            )}
                        </CardHeader>
                    </Card>
                </Link>
            ))}
        </div>
    )
}

export default PostList