import { Separator } from "./ui/separator"
import { MessageSquare, Github, Twitter, Heart } from "lucide-react"
import Link from "next/link"

export default function Footer() {
    return (
        <footer className="border-t bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
            <div className="container mx-auto max-w-6xl px-4 py-8">
                <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
                    {/* Brand */}
                    <div className="space-y-4">
                        <div className="flex items-center space-x-2">
                            <MessageSquare className="h-6 w-6 text-primary" />
                            <span className="font-bold text-lg">Discuss</span>
                        </div>
                        <p className="text-sm text-muted-foreground">
                            A modern discussion platform built with Next.js and shadcn/ui.
                        </p>
                    </div>

                    {/* Navigation */}
                    <div className="space-y-4">
                        <h3 className="font-semibold">Navigation</h3>
                        <ul className="space-y-2 text-sm">
                            <li>
                                <Link href="/" className="text-muted-foreground hover:text-foreground transition-colors">
                                    Home
                                </Link>
                            </li>
                            <li>
                                <Link href="/topics" className="text-muted-foreground hover:text-foreground transition-colors">
                                    Topics
                                </Link>
                            </li>
                            <li>
                                <Link href="/search" className="text-muted-foreground hover:text-foreground transition-colors">
                                    Search
                                </Link>
                            </li>
                        </ul>
                    </div>

                    {/* Community */}
                    <div className="space-y-4">
                        <h3 className="font-semibold">Community</h3>
                        <ul className="space-y-2 text-sm">
                            <li>
                                <span className="text-muted-foreground">Guidelines</span>
                            </li>
                            <li>
                                <span className="text-muted-foreground">Help</span>
                            </li>
                            <li>
                                <span className="text-muted-foreground">Support</span>
                            </li>
                        </ul>
                    </div>

                    {/* Social */}
                    <div className="space-y-4">
                        <h3 className="font-semibold">Connect</h3>
                        <div className="flex space-x-4">
                            <Github className="h-5 w-5 text-muted-foreground hover:text-foreground transition-colors cursor-pointer" />
                            <Twitter className="h-5 w-5 text-muted-foreground hover:text-foreground transition-colors cursor-pointer" />
                        </div>
                    </div>
                </div>

                <Separator className="my-8" />

                <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
                    <p className="text-sm text-muted-foreground">
                        © 2024 Discuss. All rights reserved.
                    </p>
                    <div className="flex items-center space-x-1 text-sm text-muted-foreground">
                        <span>Made with</span>
                        <Heart className="h-4 w-4 text-red-500" />
                        <span>using Next.js & shadcn/ui</span>
                    </div>
                </div>
            </div>
        </footer>
    )
}
