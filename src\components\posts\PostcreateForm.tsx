'use client'
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
    Dialog,
    DialogClose,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { useActionState } from "react"
import { createPost } from "@/actions/create-post"

type PostCreateFormProps = {
    slug: string
}

const PostCreateForm = ({ slug }: PostCreateFormProps) => {
    const [formState, action] = useActionState(createPost.bind(null, slug), { errors: {} });
    return (
        <Dialog>
            <DialogTrigger asChild>
                <Button>Create a Post</Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[425px]">
                <form action={action} >
                    <DialogHeader>
                        <DialogTitle>Create a New Post</DialogTitle>
                        <DialogDescription>
                            Write a new post. Click save when you are done.
                        </DialogDescription>
                    </DialogHeader>
                    <div className="grid gap-4">
                        <div className="grid gap-3">
                            <Label htmlFor="title">Name</Label>
                            <Input id="title" name="title" />
                        </div>
                        {formState.errors.title && <p className="text-sm text-red-600">{formState.errors.title.join(', ')}</p>}
                        <div className="grid gap-3">
                            <Label htmlFor="content">Content</Label>
                            <Textarea id="content" name="content" />
                            {/* {formState.errors.content && <p className="text-sm text-red-600">{formState.errors.content.join(', ')}</p>} */}
                            {formState.errors.formError && <p className="border border-red-500 bg-red-200">{formState.errors.formError.join(', ')}</p>}
                        </div>
                    </div>
                    <DialogFooter>
                        <Button type="submit" className="w-full ">Save changes</Button>
                    </DialogFooter>
                </form>
            </DialogContent>
        </Dialog>
    )
}
export default PostCreateForm;
