'use client'
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
    <PERSON>alog,
    <PERSON>alogClose,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { useActionState, useState } from "react"
import { createPost } from "@/actions/create-post"
import { PenSquare, AlertCircle } from "lucide-react"

type PostCreateFormProps = {
    slug: string
}

const PostCreateForm = ({ slug }: PostCreateFormProps) => {
    const [formState, action] = useActionState(createPost.bind(null, slug), { errors: {} });
    const [open, setOpen] = useState(false);

    return (
        <Dialog open={open} onOpenChange={setOpen}>
            <DialogTrigger asChild>
                <Button className="w-full">
                    <PenSquare className="h-4 w-4 mr-2" />
                    Create Post
                </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[600px]">
                <form action={action} className="space-y-6">
                    <DialogHeader className="space-y-3">
                        <div className="flex items-center space-x-2">
                            <PenSquare className="h-5 w-5 text-primary" />
                            <DialogTitle className="text-xl">Create a New Post</DialogTitle>
                        </div>
                        <DialogDescription className="text-base">
                            Share your thoughts and start a discussion in #{slug}
                        </DialogDescription>
                    </DialogHeader>

                    <div className="space-y-4">
                        <div className="space-y-2">
                            <Label htmlFor="title" className="text-sm font-medium">
                                Post Title
                            </Label>
                            <Input
                                id="title"
                                name="title"
                                placeholder="What's your post about?"
                                className="transition-all focus:ring-2 focus:ring-primary/20"
                            />
                            {formState.errors.title && (
                                <div className="flex items-center space-x-2 text-sm text-destructive">
                                    <AlertCircle className="h-4 w-4" />
                                    <span>{formState.errors.title.join(', ')}</span>
                                </div>
                            )}
                        </div>

                        <div className="space-y-2">
                            <Label htmlFor="content" className="text-sm font-medium">
                                Content
                            </Label>
                            <Textarea
                                id="content"
                                name="content"
                                placeholder="Share your thoughts, ask questions, or start a discussion..."
                                className="min-h-[150px] transition-all focus:ring-2 focus:ring-primary/20"
                            />
                            {formState.errors.content && (
                                <div className="flex items-center space-x-2 text-sm text-destructive">
                                    <AlertCircle className="h-4 w-4" />
                                    <span>{formState.errors.content.join(', ')}</span>
                                </div>
                            )}
                        </div>

                        {formState.errors.formError && (
                            <div className="p-3 rounded-md bg-destructive/10 border border-destructive/20">
                                <div className="flex items-center space-x-2 text-sm text-destructive">
                                    <AlertCircle className="h-4 w-4" />
                                    <span>{formState.errors.formError.join(', ')}</span>
                                </div>
                            </div>
                        )}
                    </div>

                    <DialogFooter className="gap-2">
                        <Button
                            type="button"
                            variant="outline"
                            onClick={() => setOpen(false)}
                        >
                            Cancel
                        </Button>
                        <Button type="submit" className="min-w-[100px]">
                            Create Post
                        </Button>
                    </DialogFooter>
                </form>
            </DialogContent>
        </Dialog>
    )
}
export default PostCreateForm;
