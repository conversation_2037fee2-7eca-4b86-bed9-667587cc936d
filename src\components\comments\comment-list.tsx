import { fetchCommentByPost } from '@/lib/query/comment'
import React from 'react'
import CommentShow from './comment-show'

type CommentListProps = {
    postId: string
}

const CommentList = async ({ postId }: CommentListProps) => {
    const comments = await fetchCommentByPost(postId);
    const topLevelComments = comments.filter((comment) => comment.parentId == null);

    return (
        <div className="space-y-6">
            <h2 className='text-xl font-semibold'>
                {topLevelComments.length === 0
                    ? 'No comments yet'
                    : `${topLevelComments.length} comment${topLevelComments.length === 1 ? '' : 's'}`
                }
            </h2>

            {topLevelComments.length === 0 ? (
                <p className="text-muted-foreground">Be the first to comment on this post!</p>
            ) : (
                <div className="space-y-4">
                    {topLevelComments.map((comment) => (
                        <CommentShow key={comment.id} postId={comment.postId} commentId={comment.id} />
                    ))}
                </div>
            )}
        </div>
    )
}

export default CommentList