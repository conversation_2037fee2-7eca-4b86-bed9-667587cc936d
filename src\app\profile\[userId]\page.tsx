import { prisma } from '@/lib'
import { notFound } from 'next/navigation'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { Calendar, MessageSquare, ThumbsUp, User } from 'lucide-react'
import Link from 'next/link'

type ProfilePageProps = {
    params: Promise<{ userId: string }>
}

export default async function ProfilePage({ params }: ProfilePageProps) {
    const { userId } = await params

    const user = await prisma.user.findUnique({
        where: { id: userId },
        include: {
            Post: {
                include: {
                    topic: { select: { slug: true } },
                    _count: { select: { comments: true, votes: true } }
                },
                orderBy: { createdAt: 'desc' },
                take: 10
            },
            Comment: {
                include: {
                    post: {
                        include: {
                            topic: { select: { slug: true } }
                        }
                    }
                },
                orderBy: { createdAt: 'desc' },
                take: 10
            },
            _count: {
                select: {
                    Post: true,
                    Comment: true,
                    votes: true
                }
            }
        }
    })

    if (!user) {
        notFound()
    }

    return (
        <div className="space-y-6">
            {/* Profile Header */}
            <Card>
                <CardHeader>
                    <div className="flex items-center space-x-4">
                        <Avatar className="h-20 w-20">
                            <AvatarImage src={user.image || ""} />
                            <AvatarFallback className="text-2xl">
                                {user.name?.charAt(0) || 'U'}
                            </AvatarFallback>
                        </Avatar>
                        
                        <div className="space-y-2">
                            <h1 className="text-3xl font-bold">{user.name || 'Anonymous User'}</h1>
                            <p className="text-muted-foreground">{user.email}</p>
                            <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                                <Calendar className="h-4 w-4" />
                                <span>Joined {new Date(user.emailVerified || '').toLocaleDateString()}</span>
                            </div>
                        </div>
                    </div>
                </CardHeader>
            </Card>

            {/* Stats */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Card>
                    <CardContent className="pt-6">
                        <div className="flex items-center space-x-2">
                            <MessageSquare className="h-5 w-5 text-primary" />
                            <div>
                                <p className="text-2xl font-bold">{user._count.Post}</p>
                                <p className="text-sm text-muted-foreground">Posts</p>
                            </div>
                        </div>
                    </CardContent>
                </Card>
                
                <Card>
                    <CardContent className="pt-6">
                        <div className="flex items-center space-x-2">
                            <User className="h-5 w-5 text-primary" />
                            <div>
                                <p className="text-2xl font-bold">{user._count.Comment}</p>
                                <p className="text-sm text-muted-foreground">Comments</p>
                            </div>
                        </div>
                    </CardContent>
                </Card>
                
                <Card>
                    <CardContent className="pt-6">
                        <div className="flex items-center space-x-2">
                            <ThumbsUp className="h-5 w-5 text-primary" />
                            <div>
                                <p className="text-2xl font-bold">{user._count.votes}</p>
                                <p className="text-sm text-muted-foreground">Votes Cast</p>
                            </div>
                        </div>
                    </CardContent>
                </Card>
            </div>

            {/* Recent Activity */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Recent Posts */}
                <Card>
                    <CardHeader>
                        <CardTitle>Recent Posts</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                        {user.Post.length === 0 ? (
                            <p className="text-muted-foreground">No posts yet</p>
                        ) : (
                            user.Post.map((post) => (
                                <div key={post.id} className="space-y-2">
                                    <Link 
                                        href={`/topics/${post.topic.slug}/posts/${post.id}`}
                                        className="font-medium hover:text-primary transition-colors"
                                    >
                                        {post.title}
                                    </Link>
                                    <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                                        <Badge variant="secondary">#{post.topic.slug}</Badge>
                                        <span>•</span>
                                        <span>{post._count.comments} comments</span>
                                        <span>•</span>
                                        <span>{new Date(post.createdAt).toLocaleDateString()}</span>
                                    </div>
                                    <Separator />
                                </div>
                            ))
                        )}
                    </CardContent>
                </Card>

                {/* Recent Comments */}
                <Card>
                    <CardHeader>
                        <CardTitle>Recent Comments</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                        {user.Comment.length === 0 ? (
                            <p className="text-muted-foreground">No comments yet</p>
                        ) : (
                            user.Comment.map((comment) => (
                                <div key={comment.id} className="space-y-2">
                                    <p className="text-sm line-clamp-2">{comment.content}</p>
                                    <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                                        <span>on</span>
                                        <Link 
                                            href={`/topics/${comment.post.topic.slug}/posts/${comment.post.id}`}
                                            className="font-medium hover:text-primary transition-colors"
                                        >
                                            {comment.post.title}
                                        </Link>
                                        <span>•</span>
                                        <span>{new Date(comment.createdAt).toLocaleDateString()}</span>
                                    </div>
                                    <Separator />
                                </div>
                            ))
                        )}
                    </CardContent>
                </Card>
            </div>
        </div>
    )
}
