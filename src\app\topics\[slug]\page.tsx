import PostList from '@/components/posts/post-list';
import PostCreateForm from '@/components/posts/PostcreateForm';
import { Button } from '@/components/ui/button';
import { fetchPostByTopicSlug } from '@/lib/query/post';
import { ChevronLeft, Hash } from 'lucide-react';
import Link from 'next/link';
import React from 'react'

type SlugParams = {
    params: Promise<{ slug: string }>
}

const TopicShowPage = async ({ params }: SlugParams) => {
    const { slug } = await params;

    return (
        <div className="space-y-6">
            {/* Breadcrumb */}
            <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                <Link href="/" className="hover:text-foreground transition-colors">
                    <Button variant="ghost" size="sm">
                        <ChevronLeft className="h-4 w-4 mr-1" />
                        Home
                    </Button>
                </Link>
                <span>/</span>
                <span className="text-foreground font-medium">{slug}</span>
            </div>

            {/* Topic Header */}
            <div className="space-y-4">
                <div className="flex items-center space-x-3">
                    <Hash className="h-8 w-8 text-primary" />
                    <h1 className="text-3xl font-bold capitalize">{slug.replace('-', ' ')}</h1>
                </div>
                <p className="text-muted-foreground">
                    Discussions about {slug.replace('-', ' ')}
                </p>
            </div>

            {/* Main Content */}
            <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
                <div className="lg:col-span-3 space-y-6">
                    <div className="flex items-center justify-between">
                        <h2 className="text-xl font-semibold">Posts</h2>
                        <div className="lg:hidden">
                            <PostCreateForm slug={slug} />
                        </div>
                    </div>
                    <PostList fetchData={() => fetchPostByTopicSlug(slug)} />
                </div>

                {/* Sidebar - Hidden on mobile */}
                <div className="hidden lg:block">
                    <div className="sticky top-24">
                        <PostCreateForm slug={slug} />
                    </div>
                </div>
            </div>
        </div>
    )
}

export default TopicShowPage