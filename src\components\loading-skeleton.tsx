import { Skeleton } from "./ui/skeleton"
import { <PERSON>, Card<PERSON>ontent, CardHeader } from "./ui/card"

export function PostListSkeleton() {
    return (
        <div className="space-y-4">
            {Array.from({ length: 3 }).map((_, i) => (
                <Card key={i}>
                    <CardHeader className="space-y-4">
                        <div className="flex space-x-4">
                            <div className="flex-shrink-0">
                                <div className="flex flex-col items-center space-y-1">
                                    <Skeleton className="h-8 w-8" />
                                    <Skeleton className="h-4 w-6" />
                                    <Skeleton className="h-8 w-8" />
                                </div>
                            </div>
                            
                            <div className="flex-1 space-y-3">
                                <Skeleton className="h-6 w-3/4" />
                                
                                <div className="flex items-center justify-between">
                                    <div className="flex items-center space-x-4">
                                        <div className="flex items-center space-x-2">
                                            <Skeleton className="h-6 w-6 rounded-full" />
                                            <Skeleton className="h-4 w-20" />
                                        </div>
                                        <Skeleton className="h-4 w-16" />
                                    </div>
                                    <Skeleton className="h-4 w-20" />
                                </div>
                                
                                <Skeleton className="h-4 w-full" />
                                <Skeleton className="h-4 w-2/3" />
                            </div>
                        </div>
                    </CardHeader>
                </Card>
            ))}
        </div>
    )
}

export function CommentSkeleton() {
    return (
        <Card>
            <CardContent className="pt-6">
                <div className="flex space-x-3">
                    <Skeleton className="h-8 w-8 rounded-full" />
                    
                    <div className="flex-1 space-y-3">
                        <div className="flex items-center space-x-2">
                            <Skeleton className="h-4 w-20" />
                            <Skeleton className="h-4 w-16" />
                        </div>
                        
                        <Skeleton className="h-4 w-full" />
                        <Skeleton className="h-4 w-3/4" />
                        
                        <Skeleton className="h-8 w-16" />
                    </div>
                </div>
            </CardContent>
        </Card>
    )
}
