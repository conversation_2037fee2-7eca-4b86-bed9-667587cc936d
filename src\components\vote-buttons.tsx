'use client'

import { But<PERSON> } from "./ui/button"
import { ChevronUp, ChevronDown } from "lucide-react"
import { vote } from "@/actions/vote"
import { VoteType } from "@/generated/prisma"
import { useSession } from "next-auth/react"
import { toast } from "sonner"

type VoteButtonsProps = {
    postId?: string
    commentId?: string
    upvotes: number
    downvotes: number
    userVote?: VoteType | null
}

export function VoteButtons({ postId, commentId, upvotes, downvotes, userVote }: VoteButtonsProps) {
    const { data: session } = useSession()
    
    const handleVote = async (voteType: VoteType) => {
        if (!session?.user) {
            toast.error("Please sign in to vote")
            return
        }

        try {
            await vote({ postId, commentId, voteType })
            toast.success("Vote recorded!")
        } catch (error) {
            toast.error("Failed to vote. Please try again.")
        }
    }

    const totalScore = upvotes - downvotes

    return (
        <div className="flex flex-col items-center space-y-1">
            <Button
                variant={userVote === VoteType.UP ? "default" : "ghost"}
                size="sm"
                onClick={() => handleVote(VoteType.UP)}
                disabled={!session?.user}
                className="h-8 w-8 p-0"
            >
                <ChevronUp className="h-4 w-4" />
            </Button>
            
            <span className={`text-sm font-medium ${
                totalScore > 0 ? 'text-green-600' : 
                totalScore < 0 ? 'text-red-600' : 
                'text-muted-foreground'
            }`}>
                {totalScore}
            </span>
            
            <Button
                variant={userVote === VoteType.DOWN ? "default" : "ghost"}
                size="sm"
                onClick={() => handleVote(VoteType.DOWN)}
                disabled={!session?.user}
                className="h-8 w-8 p-0"
            >
                <ChevronDown className="h-4 w-4" />
            </Button>
        </div>
    )
}
