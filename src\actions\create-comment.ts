'use server'
import { auth } from "@/auth"
import { prisma } from "@/lib"
import { revalidatePath } from "next/cache"
import { z } from "zod"

const createCommentSchema = z.object({
    content: z.string().min(3)
})

type CreateCommentState = {
    errors: {
        content?: string[],
        formError?: string[]
    }
}

export const createComment = async ({ postId, parentID }: { postId: string, parentID?: string }, prevState: CreateCommentState, formData: FormData): Promise<CreateCommentState> => {
    const res = createCommentSchema.safeParse({
        content: formData.get('content')
    })

    if (!res.success) {
        return {
            errors: res.error.flatten().fieldErrors
        }
    }

    const session = await auth();
    if (!session || !session.user || !session.user.id) {
        return {
            errors: {
                formError: ["You have to login first"]
            }
        }
    }
    // let comment = 
    try {
        await prisma.comment.create({
            data: {
                content: res.data.content,
                postId: postId,
                userId: session.user.id,
                parentId: parentID
            }
        })
    } catch (error: unknown) {
        if (error instanceof Error) {
            return {
                errors: {
                    formError: [error.message]
                }
            }
        } else {
            return {
                errors: {
                    formError: ["Something went wrong."]
                }
            }
        }
    }

    const topic = await prisma.topic.findFirst({
        where: {
            posts: {
                some: {
                    id: postId
                }
            }
        }
    })

    if (!topic) {
        return {
            errors: {
                formError: ["Topic not found."]
            }
        }
    }
    revalidatePath(`/topics/${topic.slug}/posts/${postId}`);
    return {
        errors: {}
    }
}