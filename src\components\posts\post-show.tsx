import { prisma } from '@/lib'
import { notFound } from 'next/navigation'
import React from 'react'

type PostShowProps = {
    postId: string
}

const PostShow = async ({ postId }: PostShowProps) => {
    const post = await prisma.post.findFirst({
        where: {
            id: postId,
        }
    })
    if (!post) notFound();

    return (
        <div>
            <h1 className="font-bold my-2 text-2xl">{post.title}</h1>
            <div className="border rounded p-4">{post.content}</div>
        </div>
    )
}

export default PostShow