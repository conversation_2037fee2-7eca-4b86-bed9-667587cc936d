import { prisma } from '@/lib'
import { notFound } from 'next/navigation'
import React from 'react'
import { Card, CardContent, CardHeader } from '../ui/card'
import { Avatar, AvatarFallback, AvatarImage } from '../ui/avatar'
import { Clock, User } from 'lucide-react'

type PostShowProps = {
    postId: string
}

const PostShow = async ({ postId }: PostShowProps) => {
    const post = await prisma.post.findFirst({
        where: {
            id: postId,
        },
        include: {
            user: true,
            topic: true,
            _count: {
                select: {
                    comments: true
                }
            }
        }
    })
    if (!post) notFound();

    return (
        <Card>
            <CardHeader className="space-y-4">
                <h1 className="text-3xl font-bold leading-tight">{post.title}</h1>

                <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                    <div className="flex items-center space-x-2">
                        <Avatar className="h-6 w-6">
                            <AvatarImage src={post.user.image || ""} />
                            <AvatarFallback className="text-xs">
                                {post.user.name?.charAt(0) || 'U'}
                            </AvatarFallback>
                        </Avatar>
                        <span>By {post.user.name}</span>
                    </div>

                    <span>•</span>

                    <div className="flex items-center space-x-1">
                        <Clock className="h-4 w-4" />
                        <span>{new Date(post.createdAt).toLocaleDateString()}</span>
                    </div>

                    <span>•</span>

                    <span>in #{post.topic.slug}</span>
                </div>
            </CardHeader>

            <CardContent>
                <div className="prose prose-sm max-w-none">
                    <p className="text-foreground leading-relaxed whitespace-pre-wrap">
                        {post.content}
                    </p>
                </div>
            </CardContent>
        </Card>
    )
}

export default PostShow