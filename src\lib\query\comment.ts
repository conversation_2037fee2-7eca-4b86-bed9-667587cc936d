import type { Comment } from "@/generated/prisma"
import { prisma } from ".."


export type CommentWithAuthour = Comment & {
    user: { name: string | null, image: string | null }
}

export const fetchCommentByPost = async (postId:string): Promise<CommentWithAuthour[]> => {
    return prisma.comment.findMany({
        where: {
            postId
        },
        include: {
            user: { select: { name: true, image: true } }
        }
    })
}