import { prisma } from '@/lib'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Hash, MessageSquare, Users } from 'lucide-react'
import Link from 'next/link'

export default async function TopicsPage() {
    const topics = await prisma.topic.findMany({
        include: {
            _count: {
                select: {
                    posts: true
                }
            },
            posts: {
                include: {
                    _count: {
                        select: {
                            comments: true
                        }
                    }
                },
                orderBy: {
                    createdAt: 'desc'
                },
                take: 1
            }
        },
        orderBy: {
            posts: {
                _count: 'desc'
            }
        }
    })

    return (
        <div className="space-y-6">
            {/* Header */}
            <div className="text-center space-y-4 py-8">
                <h1 className="text-4xl font-bold bg-gradient-to-r from-primary to-primary/60 bg-clip-text text-transparent">
                    Explore Topics
                </h1>
                <p className="text-muted-foreground text-lg max-w-2xl mx-auto">
                    Discover discussions across various topics and join the conversation.
                </p>
            </div>

            {/* Topics Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {topics.map((topic) => {
                    const totalComments = topic.posts.reduce((sum, post) => sum + post._count.comments, 0)
                    const latestPost = topic.posts[0]

                    return (
                        <Link key={topic.id} href={`/topics/${topic.slug}`}>
                            <Card className="h-full hover:shadow-lg transition-all duration-200 hover:scale-[1.02] cursor-pointer group">
                                <CardHeader>
                                    <div className="flex items-center space-x-3">
                                        <div className="p-2 rounded-lg bg-primary/10 group-hover:bg-primary/20 transition-colors">
                                            <Hash className="h-5 w-5 text-primary" />
                                        </div>
                                        <div>
                                            <CardTitle className="capitalize group-hover:text-primary transition-colors">
                                                {topic.slug.replace('-', ' ')}
                                            </CardTitle>
                                            <CardDescription className="line-clamp-2">
                                                {topic.description}
                                            </CardDescription>
                                        </div>
                                    </div>
                                </CardHeader>
                                
                                <CardContent className="space-y-4">
                                    {/* Stats */}
                                    <div className="flex items-center justify-between text-sm">
                                        <div className="flex items-center space-x-4">
                                            <div className="flex items-center space-x-1 text-muted-foreground">
                                                <MessageSquare className="h-4 w-4" />
                                                <span>{topic._count.posts} posts</span>
                                            </div>
                                            <div className="flex items-center space-x-1 text-muted-foreground">
                                                <Users className="h-4 w-4" />
                                                <span>{totalComments} comments</span>
                                            </div>
                                        </div>
                                    </div>

                                    {/* Latest Post */}
                                    {latestPost && (
                                        <div className="space-y-2">
                                            <p className="text-xs text-muted-foreground uppercase tracking-wide">
                                                Latest Post
                                            </p>
                                            <p className="text-sm font-medium line-clamp-2">
                                                {latestPost.title}
                                            </p>
                                            <p className="text-xs text-muted-foreground">
                                                {new Date(latestPost.createdAt).toLocaleDateString()}
                                            </p>
                                        </div>
                                    )}
                                </CardContent>
                            </Card>
                        </Link>
                    )
                })}
            </div>

            {topics.length === 0 && (
                <div className="text-center py-12">
                    <Hash className="mx-auto h-12 w-12 text-muted-foreground" />
                    <h3 className="mt-4 text-lg font-semibold">No topics yet</h3>
                    <p className="text-muted-foreground">Be the first to create a topic!</p>
                </div>
            )}
        </div>
    )
}
