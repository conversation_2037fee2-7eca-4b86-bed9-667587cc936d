'use client'
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
    <PERSON>alog,
    <PERSON>alogClose,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { createTopic } from "@/actions/create-topic"
import { useActionState } from "react"

const TopicCreateForm = () => {
    const [formState, action] = useActionState(createTopic, { errors: {} });
    return (
        <Dialog>
            <DialogTrigger asChild>
                <Button>New Topic</Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[425px]">
                <form action={action} >
                    <DialogHeader>
                        <DialogTitle>Create a New Topic</DialogTitle>
                        <DialogDescription>
                            Enter a name and description for your new topic.
                        </DialogDescription>
                    </DialogHeader>
                    <div className="grid gap-4">
                        <div className="grid gap-3">
                            <Label htmlFor="name">Name</Label>
                            <Input id="name" name="name" />
                        </div>
                        {formState.errors.name && <p className="text-sm text-red-600">{formState.errors.name.join(', ')}</p>}
                        <div className="grid gap-3">
                            <Label htmlFor="description">Description</Label>
                            <Textarea id="description" name="description" />
                            {formState.errors.description && <p className="text-sm text-red-600">{formState.errors.description.join(', ')}</p>}
                            {formState.errors.formError && <p className="border border-red-500 bg-red-200">{formState.errors.formError.join(', ')}</p>}
                        </div>
                    </div>
                    <DialogFooter>
                        <Button type="submit" className="w-full ">Save changes</Button>
                    </DialogFooter>
                </form>
            </DialogContent>
        </Dialog>
    )
}
export default TopicCreateForm;
