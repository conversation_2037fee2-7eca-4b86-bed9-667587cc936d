'use client'
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
    <PERSON>alog,
    <PERSON>alog<PERSON>lose,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { createTopic } from "@/actions/create-topic"
import { useActionState, useState } from "react"
import { Plus, Hash, AlertCircle } from "lucide-react"
import { toast } from "sonner"

const TopicCreateForm = () => {
    const [formState, action] = useActionState(createTopic, { errors: {} });
    const [open, setOpen] = useState(false);

    return (
        <Dialog open={open} onOpenChange={setOpen}>
            <DialogTrigger asChild>
                <Button className="w-full group">
                    <Plus className="h-4 w-4 mr-2 group-hover:rotate-90 transition-transform" />
                    New Topic
                </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[500px]">
                <form action={action} className="space-y-6">
                    <DialogHeader className="space-y-3">
                        <div className="flex items-center space-x-2">
                            <Hash className="h-5 w-5 text-primary" />
                            <DialogTitle className="text-xl">Create a New Topic</DialogTitle>
                        </div>
                        <DialogDescription className="text-base">
                            Start a new discussion topic for the community to explore and engage with.
                        </DialogDescription>
                    </DialogHeader>

                    <div className="space-y-4">
                        <div className="space-y-2">
                            <Label htmlFor="name" className="text-sm font-medium">
                                Topic Name
                            </Label>
                            <Input
                                id="name"
                                name="name"
                                placeholder="e.g., javascript, react, web-development"
                                className="transition-all focus:ring-2 focus:ring-primary/20"
                            />
                            {formState.errors.name && (
                                <div className="flex items-center space-x-2 text-sm text-destructive">
                                    <AlertCircle className="h-4 w-4" />
                                    <span>{formState.errors.name.join(', ')}</span>
                                </div>
                            )}
                        </div>

                        <div className="space-y-2">
                            <Label htmlFor="description" className="text-sm font-medium">
                                Description
                            </Label>
                            <Textarea
                                id="description"
                                name="description"
                                placeholder="Describe what this topic is about..."
                                className="min-h-[100px] transition-all focus:ring-2 focus:ring-primary/20"
                            />
                            {formState.errors.description && (
                                <div className="flex items-center space-x-2 text-sm text-destructive">
                                    <AlertCircle className="h-4 w-4" />
                                    <span>{formState.errors.description.join(', ')}</span>
                                </div>
                            )}
                        </div>

                        {formState.errors.formError && (
                            <div className="p-3 rounded-md bg-destructive/10 border border-destructive/20">
                                <div className="flex items-center space-x-2 text-sm text-destructive">
                                    <AlertCircle className="h-4 w-4" />
                                    <span>{formState.errors.formError.join(', ')}</span>
                                </div>
                            </div>
                        )}
                    </div>

                    <DialogFooter className="gap-2">
                        <Button
                            type="button"
                            variant="outline"
                            onClick={() => setOpen(false)}
                        >
                            Cancel
                        </Button>
                        <Button type="submit" className="min-w-[100px]">
                            Create Topic
                        </Button>
                    </DialogFooter>
                </form>
            </DialogContent>
        </Dialog>
    )
}
export default TopicCreateForm;
