'use server'

import { auth } from "@/auth"
import { prisma } from "@/lib"
import { revalidatePath } from "next/cache"
import { VoteType } from "@/generated/prisma"

type VoteActionProps = {
    postId?: string
    commentId?: string
    voteType: VoteType
}

export async function vote({ postId, commentId, voteType }: VoteActionProps) {
    const session = await auth()
    
    if (!session || !session.user || !session.user.id) {
        throw new Error("You must be logged in to vote")
    }

    const userId = session.user.id

    try {
        // Check if user has already voted on this item
        const existingVote = await prisma.vote.findFirst({
            where: {
                userId,
                ...(postId ? { postId } : { commentId })
            }
        })

        if (existingVote) {
            if (existingVote.type === voteType) {
                // Remove vote if clicking the same vote type
                await prisma.vote.delete({
                    where: { id: existingVote.id }
                })
            } else {
                // Update vote type if clicking different vote type
                await prisma.vote.update({
                    where: { id: existingVote.id },
                    data: { type: voteType }
                })
            }
        } else {
            // Create new vote
            await prisma.vote.create({
                data: {
                    userId,
                    type: voteType,
                    ...(postId ? { postId } : { commentId })
                }
            })
        }

        // Revalidate the page to show updated vote counts
        if (postId) {
            revalidatePath(`/topics/*/posts/${postId}`)
        }
        revalidatePath('/')
        
    } catch (error) {
        console.error('Error voting:', error)
        throw new Error("Failed to vote")
    }
}
